from selenium.webdriver.chrome.service import Service
import os, sys, re, time, requests, cloudscraper, yaml
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from urllib.parse import urljoin, urlparse
from datetime import datetime


########################################################################
# 加载配置文件
def load_config(config_path="config.yml"):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 设置默认日期（上一个月）
        today = datetime.now()
        if today.month == 1:
            default_year, default_month = today.year - 1, 12
        else:
            default_year, default_month = today.year, today.month - 1
        
        # 如果配置中的年月为空，使用默认值
        date_filter = config.get('crawl', {}).get('date_filter', {})
        if date_filter.get('year') is None:
            date_filter['year'] = default_year
        if date_filter.get('month') is None:
            date_filter['month'] = default_month
        
        return config
    except FileNotFoundError:
        print(f"配置文件 {config_path} 不存在，使用默认配置")
        return None
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None

# 加载配置
config = load_config()

# 从配置获取设置，如果没有配置则使用默认值
if config:
    SKIP_KEYWORDS = tuple(config.get('crawl', {}).get('skip_keywords', [
        '中字後補', '简中补字', 'Chinese Sub', '中文字幕後補'
    ]))
    QUALITY_PRIORITY = config.get('crawl', {}).get('quality_priority', ['1080', '720', '480'])
    DOWNLOAD_DIR = config.get('download', {}).get('download_dir', 'downloads')
    ORGANIZE_BY_DATE = config.get('download', {}).get('organize_by_date', True)
    TARGET_YEAR = config.get('crawl', {}).get('date_filter', {}).get('year', 2025)
    TARGET_MONTHS = [config.get('crawl', {}).get('date_filter', {}).get('month', 6)]
else:
    # 默认配置
    SKIP_KEYWORDS = ('中字後補', '简中补字', 'Chinese Sub', '中文字幕後補')
    QUALITY_PRIORITY = ['1080', '720', '480']
    DOWNLOAD_DIR = 'downloads'
    ORGANIZE_BY_DATE = True
    TARGET_YEAR = 2025
    TARGET_MONTHS = [6]

########################################################################
def should_skip(title: str) -> bool:
    """标题包含屏蔽词 → True"""
    return any(kw.lower() in title.lower() for kw in SKIP_KEYWORDS)
########################################################################


def strip_brackets(title: str) -> str:
    """去掉任意位置的 [xxx]，并压缩多余空白"""
    # ① 去掉任何 '[ … ]' 及其两侧空白
    no_brackets = re.sub(r'\s*\[[^\]]+\]\s*', ' ', title)
    # ② 把连续空白合并为 1 个空格，再 strip
    return re.sub(r'\s+', ' ', no_brackets).strip()

def run_crawler():
    """主爬虫函数，可以被外部调用"""
    print("开始执行 hanime1.me 爬虫...")
    
    year = TARGET_YEAR
    for month in TARGET_MONTHS:
        base_url = ("https://hanime1.me/search?query=&type=&genre=%E8%A3%8F%E7%95%AA"
                    "&sort=&year={}&month={}")
        url = base_url.format(year, month)

        scraper = cloudscraper.create_scraper()
        response = scraper.get(url, timeout=15)
        soup = BeautifulSoup(response.text, "html.parser")

        # ---------- ① 在一级页同时抓封面 ----------
        cover_map = {}                                  # {watch_link: cover_url}
        for a in soup.select('a[href^="https://hanime1.me/watch"]'):
            href = a.get("href")
            img  = a.find("img")
            if not href or not img:
                continue
            cover = img.get("src") or img.get("data-src")
            if cover:
                cover_map[href] = urljoin(url, cover)

        pattern  = r'"(https://hanime1\.me/watch\?[^\s]+)"'
        matches  = re.findall(pattern, str(soup))       # 仍用你的正则
        
        # 设置下载目录
        if ORGANIZE_BY_DATE:
            download_dir = os.path.join(DOWNLOAD_DIR, str(year), f"{month:02d}")
        else:
            download_dir = DOWNLOAD_DIR
        
        os.makedirs(download_dir, exist_ok=True)
        print("目录:", download_dir, "  总链接:", len(matches))

        # ---------- ② 每条播放页 ----------
        for idx, matche in enumerate(matches, 1):
            print(f"\n[{idx}/{len(matches)}] 解析 {matche}")
            # ---------- 创建安静的 Chrome ----------
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # 无头模式，必须
            chrome_options.add_argument("--no-sandbox")  # root 用户必须加，否则报错
            chrome_options.add_argument('--disable-dev-shm-usage')

            chrome_options.add_argument('--remote-debugging-pipe')   # ① 不再监听端口
            chrome_options.add_argument('--disable-logging')         # ②
            chrome_options.add_argument('--log-level=3')             # ② 只保留 fatal

            chrome_options.add_experimental_option(                  # ③
                "excludeSwitches", ["enable-logging"])

            chrome_options.add_argument(
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                'AppleWebKit/537.36 (KHTML, like Gecko) '
                'Chrome/********* Safari/537.36')

            service = Service(log_path=os.devnull)                   # ④

            driver = webdriver.Chrome(
                service=service,
                options=chrome_options
            )

            driver.get(matche)
            time.sleep(3)
            soup2 = BeautifulSoup(driver.page_source, 'html.parser')
            driver.quit()

            title = soup2.find('h3', id='shareBtn-title').text  # ④
            
            if should_skip(title):
                print("   跳过:", title)
                continue
            title = strip_brackets(title)
            file_title  = title
            src  = None                                                   # 视频 URL

            for q in QUALITY_PRIORITY:                                   # 使用配置的质量优先级
                tag = soup2.find('source', {'size': q})
                if tag and tag.get('src'):
                    src = tag['src']; print(q + "p URL:", src); break
            if not src:
                print("!! 找不到视频源，跳过"); continue

            # ---------- 下载视频 ----------
            video_path = os.path.join(download_dir, file_title + '.mp4')
            if not os.path.exists(video_path):
                print("   下载视频…")
                with requests.get(src, stream=True, timeout=30) as r:
                    r.raise_for_status()
                    with open(video_path, 'wb') as f:
                        for chunk in r.iter_content(8192):
                            if chunk: f.write(chunk)
                print("   ✓ 视频完成:", video_path)
            else:
                print("   ✓ 视频已存在")

            # ---------- ③ 下载封面 ----------
            cover_url = cover_map.get(matche)
            if cover_url:
                ext  = os.path.splitext(urlparse(cover_url).path)[1] or '.jpg'
                cover_path = os.path.join(download_dir, file_title+'-poster' + ext)
                if not os.path.exists(cover_path):
                    print("   下载封面…")
                    try:
                        with requests.get(cover_url, timeout=15) as r:
                            r.raise_for_status()
                            with open(cover_path, 'wb') as f:
                                f.write(r.content)
                        print("   ✓ 封面完成:", cover_path)
                    except Exception as e:
                        print("   ✗ 封面失败:", e)
                else:
                    print("   ✓ 封面已存在")
            else:
                print("   !! 在一级页没找到封面 URL")

        print(f'月份 {month} 处理完毕\n')

    print(f'年份 {year} 全部结束')
    print("爬虫执行完成！")
    return True

# 如果直接运行此文件，则执行爬虫
if __name__ == "__main__":
    run_crawler()
