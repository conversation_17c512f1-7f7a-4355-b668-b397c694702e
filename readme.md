# 安装 venv 模块（如果未装）
apt install python3-venv -y  

# 创建虚拟环境
python3 -m venv /root/venv  

# 激活虚拟环境
source /root/venv/bin/activate  

# 安装依赖
pip install -r requirements.txt

# 安装chromedriver
apt install -y libnss3 libatk1.0-0 libatk-bridge2.0-0 libcups2 libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libasound2 libpangocairo-1.0-0 libpango-1.0-0 fonts-liberation libappindicator3-1 libxss1 lsb-release xdg-utils wget

# 运行
python3 main.py