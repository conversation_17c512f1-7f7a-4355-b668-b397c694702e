#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
123网盘官方API管理工具
完全基于123网盘官方API实现，不依赖第三方库
"""

import os
import sys
import json
import yaml
import argparse
import hashlib
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
import http.client
import urllib.parse
import mimetypes

try:
    import colorama
    from colorama import Fore, Style
    colorama.init()
except ImportError:
    # 如果没有colorama，使用空字符串
    class Fore:
        RED = GREEN = YELLOW = CYAN = BLUE = WHITE = ""
    class Style:
        RESET_ALL = ""

class Pan123Official:
    """123网盘官方API管理器"""
    
    def __init__(self, config_path: str = "123_config.yml"):
        """初始化管理器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.base_url = "open-api.123pan.com"
        self.access_token = self.config.get('auth', {}).get('access_token', '')
        
        if not self.access_token:
            print(f"{Fore.RED}请在配置文件中设置access_token{Style.RESET_ALL}")
            sys.exit(1)
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"{Fore.RED}配置文件 {self.config_path} 不存在{Style.RESET_ALL}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"{Fore.RED}配置文件格式错误: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, headers: Dict = None) -> Dict:
        """发送HTTP请求"""
        try:
            conn = http.client.HTTPSConnection(self.base_url)
            
            # 默认headers
            default_headers = {
                'Content-Type': 'application/json',
                'Platform': 'open_platform',
                'Authorization': f'Bearer {self.access_token}'
            }
            
            if headers:
                default_headers.update(headers)
            
            # 准备请求数据
            payload = json.dumps(data) if data else None
            
            # 发送请求
            conn.request(method, endpoint, payload, default_headers)
            response = conn.getresponse()
            response_data = response.read()
            
            # 解析响应
            result = json.loads(response_data.decode('utf-8'))
            conn.close()
            
            return result
            
        except Exception as e:
            print(f"{Fore.RED}API请求失败: {e}{Style.RESET_ALL}")
            return {'code': -1, 'message': str(e)}
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f}{unit}"
            size /= 1024.0
        return f"{size:.1f}PB"
    
    def list_files(self, parent_id: int = 0, limit: int = 100) -> List[Dict]:
        """列出文件和文件夹"""
        try:
            # 使用官方API获取文件列表
            data = {
                "parentFileId": parent_id,
                "page": 1,
                "limit": limit,
                "orderBy": "file_name",
                "orderDirection": "asc"
            }
            
            result = self.make_request('POST', '/api/v1/file/list', data)
            
            if result.get('code') == 0:
                files = result.get('data', {}).get('infoList', [])
                
                if not files:
                    print(f"{Fore.YELLOW}文件夹为空{Style.RESET_ALL}")
                    return []
                
                print(f"\n{Fore.CYAN}文件列表:{Style.RESET_ALL}")
                print(f"{'类型':<6} {'名称':<30} {'大小':<12} {'修改时间':<20} {'ID'}")
                print("-" * 80)
                
                for file_info in files:
                    file_type = "文件夹" if file_info.get('type') == 1 else "文件"
                    name = str(file_info.get('filename', 'Unknown'))[:28]
                    size = self.format_size(file_info.get('size', 0)) if file_info.get('type') != 1 else '-'
                    modified = str(file_info.get('updateAt', 'Unknown'))[:19]
                    file_id = str(file_info.get('fileId', 'Unknown'))
                    
                    color = Fore.BLUE if file_type == "文件夹" else Fore.WHITE
                    print(f"{color}{file_type:<6} {name:<30} {size:<12} {modified:<20} {file_id}{Style.RESET_ALL}")
                
                return files
            else:
                print(f"{Fore.RED}获取文件列表失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return []
                
        except Exception as e:
            print(f"{Fore.RED}获取文件列表失败: {e}{Style.RESET_ALL}")
            return []
    
    def create_folder(self, name: str, parent_id: int = 0) -> Optional[Dict]:
        """创建文件夹"""
        try:
            data = {
                "parentFileId": parent_id,
                "filename": name
            }
            
            result = self.make_request('POST', '/api/v1/file/mkdir', data)
            
            if result.get('code') == 0:
                print(f"{Fore.GREEN}文件夹创建成功: {name}{Style.RESET_ALL}")
                return result.get('data')
            else:
                print(f"{Fore.RED}文件夹创建失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return None
                
        except Exception as e:
            print(f"{Fore.RED}创建文件夹失败: {e}{Style.RESET_ALL}")
            return None
    
    def delete_file(self, file_id: int) -> bool:
        """删除文件或文件夹"""
        try:
            # 获取文件信息用于确认
            file_info = self.get_file_info(file_id)
            if not file_info:
                return False
            
            filename = file_info.get('filename', 'Unknown')
            file_type = "文件夹" if file_info.get('type') == 1 else "文件"
            
            # 确认删除
            confirm = input(f"{Fore.YELLOW}确认删除{file_type}: {filename}? (y/N): {Style.RESET_ALL}")
            if confirm.lower() != 'y':
                print(f"{Fore.YELLOW}取消删除{Style.RESET_ALL}")
                return False
            
            data = {
                "fileIdList": [file_id]
            }
            
            result = self.make_request('POST', '/api/v1/file/trash', data)
            
            if result.get('code') == 0:
                print(f"{Fore.GREEN}{file_type}删除成功: {filename}{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}删除失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}删除失败: {e}{Style.RESET_ALL}")
            return False
    
    def get_file_info(self, file_id: int) -> Optional[Dict]:
        """获取文件信息"""
        try:
            data = {
                "fileId": file_id
            }
            
            result = self.make_request('POST', '/api/v1/file/info', data)
            
            if result.get('code') == 0:
                return result.get('data')
            else:
                print(f"{Fore.RED}获取文件信息失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return None
                
        except Exception as e:
            print(f"{Fore.RED}获取文件信息失败: {e}{Style.RESET_ALL}")
            return None

    def get_upload_url(self, parent_id: int, filename: str, size: int) -> Optional[Dict]:
        """获取上传URL"""
        try:
            # 计算文件etag（这里使用文件名和大小的组合）
            etag = hashlib.md5(f"{filename}_{size}".encode('utf-8')).hexdigest()

            data = {
                "parentFileId": parent_id,
                "filename": filename,
                "etag": etag,
                "size": size
            }

            result = self.make_request('POST', '/upload/v2/file/create', data)

            if result.get('code') == 0:
                return result.get('data')
            else:
                print(f"{Fore.RED}获取上传URL失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}获取上传URL失败: {e}{Style.RESET_ALL}")
            return None

    def upload_file(self, file_path: str, parent_id: int = 0) -> bool:
        """上传文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"{Fore.RED}文件不存在: {file_path}{Style.RESET_ALL}")
                return False

            file_size = file_path.stat().st_size
            filename = file_path.name

            print(f"{Fore.CYAN}正在上传文件: {filename} ({self.format_size(file_size)}){Style.RESET_ALL}")

            # 获取上传URL
            upload_info = self.get_upload_url(parent_id, filename, file_size)
            if not upload_info:
                return False

            # 检查是否需要上传（秒传）
            if upload_info.get('reuse', False):
                print(f"{Fore.GREEN}文件秒传成功: {filename}{Style.RESET_ALL}")
                return True

            # 获取上传URL和参数
            upload_url = upload_info.get('presignedUrls', {}).get('urls', [])
            if not upload_url:
                print(f"{Fore.RED}未获取到上传URL{Style.RESET_ALL}")
                return False

            # 上传文件
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # 使用第一个上传URL（简化处理）
            url_info = upload_url[0]
            upload_endpoint = url_info.get('url', '')

            # 解析上传URL
            from urllib.parse import urlparse
            parsed_url = urlparse(upload_endpoint)

            # 上传文件数据
            conn = http.client.HTTPSConnection(parsed_url.netloc)
            headers = {
                'Content-Type': 'application/octet-stream',
                'Content-Length': str(file_size)
            }

            conn.request('PUT', parsed_url.path + '?' + parsed_url.query, file_data, headers)
            response = conn.getresponse()

            if response.status == 200:
                # 完成上传
                complete_data = {
                    "fileId": upload_info.get('fileId'),
                    "etag": upload_info.get('etag')
                }

                result = self.make_request('POST', '/upload/v2/file/upload_complete', complete_data)

                if result.get('code') == 0:
                    print(f"{Fore.GREEN}文件上传成功: {filename}{Style.RESET_ALL}")
                    return True
                else:
                    print(f"{Fore.RED}上传完成确认失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                    return False
            else:
                print(f"{Fore.RED}文件上传失败: HTTP {response.status}{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.RED}上传文件失败: {e}{Style.RESET_ALL}")
            return False

    def download_file(self, file_id: int, save_path: str = None) -> bool:
        """下载文件"""
        try:
            # 获取文件信息
            file_info = self.get_file_info(file_id)
            if not file_info:
                return False

            filename = file_info.get('filename', f'file_{file_id}')
            file_size = file_info.get('size', 0)

            # 确定保存路径
            if save_path is None:
                save_path = filename
            elif os.path.isdir(save_path):
                save_path = os.path.join(save_path, filename)

            print(f"{Fore.CYAN}正在下载文件: {filename} ({self.format_size(file_size)}){Style.RESET_ALL}")

            # 获取下载链接
            data = {
                "fileId": file_id
            }

            result = self.make_request('POST', '/api/v1/file/download_info', data)

            if result.get('code') != 0:
                print(f"{Fore.RED}获取下载链接失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return False

            download_url = result.get('data', {}).get('DownloadURL', '')
            if not download_url:
                print(f"{Fore.RED}未获取到下载链接{Style.RESET_ALL}")
                return False

            # 下载文件
            from urllib.parse import urlparse
            parsed_url = urlparse(download_url)

            conn = http.client.HTTPSConnection(parsed_url.netloc)
            conn.request('GET', parsed_url.path + '?' + parsed_url.query)
            response = conn.getresponse()

            if response.status == 200:
                with open(save_path, 'wb') as f:
                    while True:
                        chunk = response.read(8192)
                        if not chunk:
                            break
                        f.write(chunk)

                print(f"{Fore.GREEN}文件下载成功: {save_path}{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}文件下载失败: HTTP {response.status}{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.RED}下载文件失败: {e}{Style.RESET_ALL}")
            return False

    def find_or_create_folder(self, name: str, parent_id: int = 0) -> Optional[Dict]:
        """查找或创建文件夹"""
        try:
            # 首先检查文件夹是否已存在
            files = self.list_files(parent_id, limit=100)
            for file_info in files:
                if (file_info.get('filename') == name and
                    file_info.get('type') == 1):  # type=1表示文件夹
                    print(f"{Fore.GREEN}找到已存在文件夹: {name} (ID: {file_info.get('fileId')}){Style.RESET_ALL}")
                    return file_info

            # 如果文件夹不存在，尝试创建
            print(f"{Fore.CYAN}正在创建文件夹: {name}{Style.RESET_ALL}")
            return self.create_folder(name, parent_id)

        except Exception as e:
            print(f"{Fore.RED}查找或创建文件夹失败: {e}{Style.RESET_ALL}")
            return None

    def upload_folder(self, folder_path: str, parent_id: int = 0, create_structure: bool = True) -> bool:
        """上传文件夹"""
        try:
            folder_path = Path(folder_path)
            if not folder_path.exists() or not folder_path.is_dir():
                print(f"{Fore.RED}文件夹不存在: {folder_path}{Style.RESET_ALL}")
                return False

            print(f"{Fore.CYAN}正在上传文件夹: {folder_path.name}{Style.RESET_ALL}")

            remote_folder_id = parent_id

            if create_structure:
                # 尝试查找或创建远程文件夹
                folder_result = self.find_or_create_folder(folder_path.name, parent_id)
                if folder_result:
                    remote_folder_id = folder_result.get('fileId', parent_id)
                    print(f"{Fore.GREEN}使用文件夹ID: {remote_folder_id}{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}无法创建文件夹，将上传到父目录 (ID: {parent_id}){Style.RESET_ALL}")
                    remote_folder_id = parent_id

            # 递归上传文件夹内容
            success_count = 0
            total_count = 0

            for item in folder_path.iterdir():
                total_count += 1
                if item.is_file():
                    if self.upload_file(str(item), remote_folder_id):
                        success_count += 1
                elif item.is_dir():
                    # 递归处理子文件夹
                    if self.upload_folder(str(item), remote_folder_id, create_structure):
                        success_count += 1

            print(f"{Fore.GREEN}文件夹上传完成: {success_count}/{total_count} 成功{Style.RESET_ALL}")
            return success_count == total_count

        except Exception as e:
            print(f"{Fore.RED}上传文件夹失败: {e}{Style.RESET_ALL}")
            return False

    def search_files(self, keyword: str) -> List[Dict]:
        """搜索文件"""
        try:
            data = {
                "keyword": keyword,
                "page": 1,
                "limit": 100
            }

            result = self.make_request('POST', '/api/v1/file/search', data)

            if result.get('code') == 0:
                files = result.get('data', {}).get('infoList', [])

                if not files:
                    print(f"{Fore.YELLOW}未找到匹配的文件{Style.RESET_ALL}")
                    return []

                print(f"\n{Fore.CYAN}搜索结果 (关键词: {keyword}):{Style.RESET_ALL}")
                print(f"{'类型':<6} {'名称':<30} {'大小':<12} {'修改时间':<20} {'ID'}")
                print("-" * 80)

                for file_info in files:
                    file_type = "文件夹" if file_info.get('type') == 1 else "文件"
                    name = str(file_info.get('filename', 'Unknown'))[:28]
                    size = self.format_size(file_info.get('size', 0)) if file_info.get('type') != 1 else '-'
                    modified = str(file_info.get('updateAt', 'Unknown'))[:19]
                    file_id = str(file_info.get('fileId', 'Unknown'))

                    color = Fore.BLUE if file_type == "文件夹" else Fore.WHITE
                    print(f"{color}{file_type:<6} {name:<30} {size:<12} {modified:<20} {file_id}{Style.RESET_ALL}")

                return files
            else:
                print(f"{Fore.RED}搜索失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return []

        except Exception as e:
            print(f"{Fore.RED}搜索失败: {e}{Style.RESET_ALL}")
            return []
