"""
hanime1.me 视频刮削模块
支持从hanime1.me获取tag、中文名称、摘要、年份等信息
提取自scape.py中的HanimeScraper功能
"""

import os
import re
import time
import json
from pathlib import Path
from lxml import html
from datetime import datetime
import requests
from bs4 import BeautifulSoup
import cloudscraper


class HanimeScraper:
    """Hanime1.me 刮削器"""
    
    def __init__(self, logger=None):
        self.logger = logger or self._create_simple_logger()
        self.scraper = cloudscraper.create_scraper()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        self.base_url = 'https://hanime1.me'

    def _create_simple_logger(self):
        """创建简单的日志记录器"""
        import logging
        logger = logging.getLogger('HanimeScraper')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def strip_brackets(self, title: str) -> str:
        """去掉任意位置的 [xxx]，并压缩多余空白"""
        # ① 去掉任何 ‘[ … ]’ 及其两侧空白
        no_brackets = re.sub(r'\s*\[[^\]]+\]\s*', ' ', title)
        # ② 把连续空白合并为 1 个空格，再 strip
        return re.sub(r'\s+', ' ', no_brackets).strip()


    def scrape_hanime1(self, video_id):
        """从hanime1.me获取视频信息"""
        video_url = f"{self.base_url}/watch?v={video_id}"
        self.logger.info(f"从hanime1.me获取信息: {video_url}")
        
        try:
            # 使用cloudscraper直接获取页面
            response = self.scraper.get(video_url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            # 保存HTML内容用于调试
            with open('debug_hanime.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("HTML内容已保存到 debug_hanime.html")
            
            tree = html.fromstring(response.content)

            # 获取图片
            image_url_elements = tree.xpath('//meta[@property="og:image"]/@content')
            image_url = ''
            if image_url_elements:
                image_url = image_url_elements[0]
                print(f"找到图片URL: {image_url}")

            
            info = {'image_url': image_url}
            # 尝试获取标题
            title_text = tree.xpath('//*[@id="shareBtn-title"]/text()')
            if title_text:
                title = title_text[0].strip()
                title = self.strip_brackets(title)
                print(title)
                info.update({
                    'title': title,
                    'originaltitle': title,
                    'sorttitle': title
                })
            # 设置合集
            # 日文/中文数字（用来识别“第六话”“間章二”等）
            KAN_NUM = "零壱弐参肆伍陸柒捌玖拾百千"

            # 常见卷号关键词
            VOL_KEYWORDS = (
                r"(前編|後編|中編|上巻|下巻|完結編|"
                r"[Vv]ol\.?\s*\d+|＃\d+|#\d+|"
                r"Part\s*\d+|part\d+)"
            )
            # 中文“第n话/集/卷/章……”
            DIG_VOL = r"第\s*(?:\d+|[一二三四五六七八九十百千%s])\s*[话話集章篇卷]" % KAN_NUM
            # 中文“間章二/章三/话4/集5”之类
            CN_VOL = (
                r"(?:間章|间章|章|篇|卷|集|话|話)"
                r"(?:\d+|[一二三四五六七八九十百千%s])" % KAN_NUM
            )
            # 综合卷关键字
            VOL_TOKEN = rf"(?:{VOL_KEYWORDS}|{DIG_VOL}|{CN_VOL})"

            def extract_collection(title: str) -> str | None:
                """返回推断出的合集名；若无法判断则返回 None"""

                # 1) 去掉首尾方括号段 [字幕组] / [中文字幕] / [1080p] 等
                base = re.sub(r"^\s*(?:\[[^\]]+])+\s*", "", title)
                base = re.sub(r"\s*(?:\[[^\]]+])+\s*$", "", base)
                # 2) 去掉首尾中文全角【…】标签
                base = re.sub(r"^\s*(?:【[^】]+】)+\s*", "", base)
                base = re.sub(r"\s*(?:【[^】]+】)+\s*$", "", base)
                # 3) 去掉书名号《…》
                base = re.sub(r"[《》]", "", base).strip()
                # 4) 去掉前缀“动画 / 剧场版”等修饰词
                base = re.sub(r"^(动画|劇場版|剧场版)\s*", "", base)

                # ── 规则 A ── 系列名 +（可选分隔符）+ 卷号/话数
                m = re.match(rf"^(?P<series>.+?)[\s・\-–—:：]*{VOL_TOKEN}", base, flags=re.IGNORECASE)
                if m:
                    return m.group("series").strip(" -_・:：")

                # ── 规则 B ── 系列名 + 分隔符 + 副标题
                m = re.match(r"^(.+?)[\s・\-–—:：]+\S+", base)
                if m:
                    return m.group(1).strip(" -_・:：")

                # ── 规则 C ── 整个标题即系列名（兜底）
                return base if len(base) > 1 else None
            collection = extract_collection(title)
            if collection:
                info['set'] = collection    # <set> 合集名
            

            

            
            # 尝试获取剧情简介
            plot_text = tree.xpath('//*[@id="player-div-wrapper"]//div[contains(@class, "video-caption-text")]/text()')
            if plot_text:
                plot_content = plot_text[0].strip()
                plot = plot_content.replace('\r\n', '<br>')
                if plot_content:
                    info['plot'] = f"<![CDATA[{plot}]]>"

            # 尝试获取中文标题
            cn_title_text = tree.xpath('//meta[@name="description"]/@content')
    
            if cn_title_text:
                cn_title = cn_title_text[0].strip().split(plot_content[:50], 1)[0]
                cn_title = re.sub(r'\s*\[.*\]$', '', cn_title)
                print(cn_title)
                info['outline'] = f"<![CDATA[{cn_title}]]>"
                info['tagline'] = cn_title
            

            # 尝试获取年份
            year_text = tree.xpath('//*[@id="player-div-wrapper"]/div[@class="video-details-wrapper hidden-sm hidden-md hidden-lg hidden-xl"]/text()')
            if year_text:
                date = year_text[0].strip().split('\xa0')[-1]
                print(date)
                info['premiered'] = date
                info['releasedate'] = date
                info['release'] = date
                year = date.split('-')[0]
                print(year)
                info['year'] = year

            # 尝试获取制作商
            studio_text = tree.xpath('//*[@id="video-artist-name"]/text()')
            if studio_text:
                studio = studio_text[0].strip()
                print(studio)
                info['studio'] = studio
                info['maker'] = studio       
            
            # 尝试获取标签
            tag_selectors = [
                '//div[text()="標籤"]/following-sibling::a/text()',
                '//div[contains(@class, "tags")]//a/text()',
                '//span[contains(@class, "tag")]/text()'
            ]
            
            for selector in tag_selectors:
                try:
                    tag_elements = tree.xpath(selector)
                    if tag_elements:
                        tags = [tag.strip() for tag in tag_elements if tag.strip()]
                        if tags:
                            info['tag'] = tags
                            info['genre'] = tags
                            print(f"找到标签: {', '.join(tags[:5])}")
                            break
                except:
                    continue

            # 添加默认信息
            info.update({
                'customrating': '里番', 
                'lockdata': 'False',
                'mpaa': '里番',
                'uncensored': 'False',

            })
            
            
            return info
            
        except Exception as e:
            self.logger.error(f"从hanime1.me获取信息失败: {e}")
            return {}



    def scrape_hanime1_by_title(self, title):
        """通过标题搜索hanime1.me"""
        try:
            search_url = f"{self.base_url}/search"
            search_title = title
            self.logger.info(f"hanime1搜索标题: {search_title}")
            
            response = self.scraper.get(
                search_url, 
                params={'query': search_title}, 
                headers=self.headers, 
                timeout=10
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            video_links = [
                f"{self.base_url}{link.get('href')}" 
                for link in soup.select('a[href*="/watch?v="]') 
                if link.get('href')
            ]
            
            if not video_links:
                self.logger.info(f"基于标题未找到视频: {search_title}")
                return {}
            
            # 使用第一个匹配结果
            video_id_match = re.search(r'v=(\d+)', video_links[0])
            if video_id_match:
                return self.scrape_hanime1(video_id_match.group(1))
            
            
            return {}
            
        except Exception as e:
            self.logger.error(f"基于标题搜索hanime1失败: {e}")
            return {}

    def scrape_video_file(self, title):
        """刮削单个视频文件"""
        return self.scrape_hanime1_by_title(title)
    
    def generate_nfo(self, info):
        """生成NFO文件内容"""
        from xml.sax.saxutils import escape
        
        # 获取基本信息，提供默认值
        title = info.get('title', '未知标题')
        plot = info.get('plot', '<![CDATA[暂无剧情简介]]>')
        outline = info.get('outline', '<![CDATA[暂无概要]]>')
        tagline = info.get('tagline', '')
        year = info.get('year', '2025')
        premiered = info.get('premiered', f'{year}-01-01')
        releasedate = info.get('releasedate', premiered)
        release = info.get('release', premiered)
        studio = info.get('studio', '未知制作商')
        maker = info.get('maker', studio)
        customrating = info.get('customrating', '里番')
        lockdata = info.get('lockdata', 'False')
        mpaa = info.get('mpaa', '里番')
        uncensored = info.get('uncensored', 'False')
        sorttitle = info.get('sorttitle', title)
        originaltitle = info.get('originaltitle', title)
        set_name = info.get('set', '')
        
        # 构建XML内容
        xml_content = ['<?xml version="1.0" encoding="utf-8" standalone="yes"?>']
        xml_content.append('<movie>')
        
        # 添加基本信息
        xml_content.append(f'  <plot>{plot}</plot>')
        xml_content.append(f'  <outline>{outline}</outline>')
        xml_content.append(f'  <customrating>{customrating}</customrating>')
        xml_content.append(f'  <lockdata>{lockdata.lower()}</lockdata>')
        xml_content.append(f'  <title>{escape(title)}</title>')
        xml_content.append(f'  <originaltitle>{escape(originaltitle)}</originaltitle>')
        xml_content.append(f'  <year>{year}</year>')
        xml_content.append(f'  <sorttitle>{escape(sorttitle)}</sorttitle>')
        xml_content.append(f'  <mpaa>{mpaa}</mpaa>')
        xml_content.append(f'  <premiered>{premiered}</premiered>')
        xml_content.append(f'  <releasedate>{releasedate}</releasedate>')
        
        if tagline:
            xml_content.append(f'  <tagline>{escape(tagline)}</tagline>')
        
        # 添加标签
        tags = info.get('tag', [])
        if tags:
            for tag in tags:
                xml_content.append(f'  <tag>{escape(tag)}</tag>')
        
        # 添加流派（与标签相同）
        genres = info.get('genre', tags)
        if genres:
            for genre in genres:
                xml_content.append(f'  <genre>{escape(genre)}</genre>')
        
        # 添加制作商信息
        xml_content.append(f'  <studio>{escape(studio)}</studio>')
        
        # 添加合集信息
        if set_name:
            xml_content.append('  <set>')
            xml_content.append(f'    <name>{escape(set_name)}</name>')
            xml_content.append('  </set>')
        
        xml_content.append(f'  <maker>{escape(maker)}</maker>')
        xml_content.append(f'  <release>{release}</release>')
        xml_content.append(f'  <uncensored>{uncensored}</uncensored>')
        xml_content.append('</movie>')
        
        return '\n'.join(xml_content)

    def download_image(self, image_url, title):
        """下载图片并保存为fanart.jpg"""
        if not image_url:
            self.logger.warning("没有找到图片URL，跳过下载")
            return False
            
        try:
            # 清理文件名中的特殊字符
            safe_title = re.sub(r'[<>:"/\\|?*]', '', title)
            safe_title = safe_title.replace(' ', '_')
            filename = f"{safe_title}-fanart.jpg"
            
            self.logger.info(f"开始下载图片: {image_url}")
            
            # 下载图片
            response = self.scraper.get(image_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # 保存图片
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图片已保存为: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"下载图片失败: {e}")
            return False

    def download_image_to_dir(self, image_url, title, target_dir):
        """下载图片并保存到指定目录"""
        if not image_url:
            self.logger.warning("没有找到图片URL，跳过下载")
            return False
            
        try:
            # 清理文件名中的特殊字符
            safe_title = title
            filename = f"{safe_title}-fanart.jpg"
            
            # 构建完整路径
            full_path = os.path.join(target_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(full_path):
                self.logger.info(f"图片已存在: {full_path}")
                return full_path
            
            self.logger.info(f"开始下载图片: {image_url}")
            
            # 下载图片
            response = self.scraper.get(image_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # 保存图片到指定目录
            with open(full_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图片已保存为: {full_path}")
            return full_path
            
        except Exception as e:
            self.logger.error(f"下载图片失败: {e}")
            return False

    


if __name__ == "__main__":
    scraper = HanimeScraper()
    video_title = "完堕ちX寝取られ家族 The Animation 後編 ～アナタ、許して。私たち、浮気セックスに本気でハマっちゃったの～"
    
    # 获取视频信息
    info = scraper.scrape_video_file(video_title)
    print("爬取的信息:")
    print(info)
    
    if info:
        # 生成NFO文件内容
        nfo_content = scraper.generate_nfo(info)
        print("\n生成的NFO文件内容:")
        print(nfo_content)
        
        # 保存NFO文件
        nfo_filename = f"{video_title}.nfo"
        with open(nfo_filename, 'w', encoding='utf-8') as f:
            f.write(nfo_content)
        
        print(f"\nNFO文件已保存为: {nfo_filename}")
        
        # 下载图片
        image_url = info.get('image_url', '')
        if image_url:
            image_filename = scraper.download_image(image_url, video_title)
            if image_filename:
                print(f"图片已下载为: {image_filename}")
            else:
                print("图片下载失败")
        else:
            print("未找到图片URL，跳过图片下载")
    else:
        print("未能获取视频信息，无法生成NFO文件")
    
