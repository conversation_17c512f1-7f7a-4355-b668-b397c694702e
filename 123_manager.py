#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
123网盘管理工具
基于123网盘官方API，提供文件上传、下载、管理等功能
"""

import os
import sys
import yaml
import argparse
from pathlib import Path
from typing import Optional, Dict, Any, List
import time

try:
    from pan123 import Pan123
    from pan123.auth import get_access_token
    from tqdm import tqdm
    import colorama
    from colorama import Fore, Style
    import requests
    import http.client
    import json
    import hashlib
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install pan123 tqdm colorama pyyaml requests")
    sys.exit(1)

# 初始化colorama
colorama.init()

class Pan123Manager:
    """123网盘管理器"""
    
    def __init__(self, config_path: str = "123_config.yml"):
        """初始化管理器"""
        self.config_path = config_path
        self.config = self.load_config()
        self.pan = None
        self.authenticate()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"{Fore.RED}配置文件 {self.config_path} 不存在{Style.RESET_ALL}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"{Fore.RED}配置文件格式错误: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"{Fore.RED}保存配置文件失败: {e}{Style.RESET_ALL}")
    
    def authenticate(self):
        """认证并初始化Pan123客户端"""
        try:
            auth_config = self.config.get('auth', {})
            access_token = auth_config.get('access_token')
            
            if not access_token:
                # 如果没有access_token，尝试获取
                client_id = auth_config.get('client_id')
                client_secret = auth_config.get('client_secret')
                
                if not client_id or not client_secret:
                    print(f"{Fore.RED}请在配置文件中设置client_id和client_secret{Style.RESET_ALL}")
                    sys.exit(1)
                
                print(f"{Fore.YELLOW}正在获取访问令牌...{Style.RESET_ALL}")
                access_token = get_access_token(client_id, client_secret)
                
                # 保存access_token到配置文件
                self.config['auth']['access_token'] = access_token
                self.save_config()
                print(f"{Fore.GREEN}访问令牌获取成功并已保存{Style.RESET_ALL}")
            
            # 初始化Pan123客户端
            self.pan = Pan123(access_token)
            print(f"{Fore.GREEN}123网盘客户端初始化成功{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}认证失败: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f}{unit}"
            size /= 1024.0
        return f"{size:.1f}PB"
    
    def upload_file(self, file_path: str, parent_id: int = 0) -> bool:
        """上传单个文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"{Fore.RED}文件不存在: {file_path}{Style.RESET_ALL}")
                return False

            file_size = file_path.stat().st_size
            print(f"{Fore.CYAN}正在上传文件: {file_path.name} ({self.format_size(file_size)}){Style.RESET_ALL}")

            # 执行上传（简化版本，pan123库会处理进度）
            result = self.pan.file.upload(parent_id, str(file_path))

            print(f"{Fore.GREEN}文件上传成功: {file_path.name}{Style.RESET_ALL}")
            return True

        except Exception as e:
            print(f"{Fore.RED}上传文件失败: {e}{Style.RESET_ALL}")
            return False
    
    def upload_folder(self, folder_path: str, parent_id: int = 0, create_structure: bool = True) -> bool:
        """上传文件夹，可选择是否保持目录结构"""
        try:
            folder_path = Path(folder_path)
            if not folder_path.exists() or not folder_path.is_dir():
                print(f"{Fore.RED}文件夹不存在: {folder_path}{Style.RESET_ALL}")
                return False

            print(f"{Fore.CYAN}正在上传文件夹: {folder_path.name}{Style.RESET_ALL}")

            remote_folder_id = parent_id

            if create_structure:
                # 尝试查找或创建远程文件夹
                folder_result = self.find_or_create_folder(folder_path.name, parent_id)
                if folder_result:
                    # 根据API返回结果获取文件夹ID
                    if 'fileId' in folder_result:
                        remote_folder_id = folder_result['fileId']
                    elif 'fileID' in folder_result:  # API可能返回fileID
                        remote_folder_id = folder_result['fileID']
                    else:
                        remote_folder_id = parent_id
                    print(f"{Fore.GREEN}使用文件夹ID: {remote_folder_id}{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}无法创建文件夹，将上传到父目录 (ID: {parent_id}){Style.RESET_ALL}")
                    remote_folder_id = parent_id

            # 递归上传文件夹内容
            success_count = 0
            total_count = 0

            for item in folder_path.iterdir():
                total_count += 1
                if item.is_file():
                    if self.upload_file(str(item), remote_folder_id):
                        success_count += 1
                elif item.is_dir():
                    # 递归处理子文件夹
                    if self.upload_folder(str(item), remote_folder_id, create_structure):
                        success_count += 1

            print(f"{Fore.GREEN}文件夹上传完成: {success_count}/{total_count} 成功{Style.RESET_ALL}")
            return success_count == total_count

        except Exception as e:
            print(f"{Fore.RED}上传文件夹失败: {e}{Style.RESET_ALL}")
            return False

    def download_file(self, file_id: str, save_path: str = None) -> bool:
        """下载文件"""
        try:
            # 获取文件信息
            file_info = self.get_file_info(file_id)
            if not file_info:
                return False

            filename = file_info.get('name', f'file_{file_id}')
            file_size = file_info.get('size', 0)

            # 确定保存路径
            if save_path is None:
                save_path = filename
            elif os.path.isdir(save_path):
                save_path = os.path.join(save_path, filename)

            print(f"{Fore.CYAN}正在下载文件: {filename} ({self.format_size(file_size)}){Style.RESET_ALL}")

            # 获取下载链接
            download_url = self.pan.file.get_download_url(file_id)

            # 下载文件
            response = requests.get(download_url, stream=True)
            response.raise_for_status()

            # 创建进度条
            with tqdm(total=file_size, unit='B', unit_scale=True, desc=filename) as pbar:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))

            print(f"{Fore.GREEN}文件下载成功: {save_path}{Style.RESET_ALL}")
            return True

        except Exception as e:
            print(f"{Fore.RED}下载文件失败: {e}{Style.RESET_ALL}")
            return False

    def list_files(self, parent_id: int = 0) -> List[Dict]:
        """列出文件和文件夹"""
        try:
            result = self.pan.file.list(parent_id, limit=100)

            # 处理API返回结果
            if isinstance(result, dict) and 'fileList' in result:
                files = result['fileList']
            elif isinstance(result, dict) and 'data' in result:
                files = result['data']
            elif isinstance(result, list):
                files = result
            else:
                files = []

            if not files:
                print(f"{Fore.YELLOW}文件夹为空{Style.RESET_ALL}")
                return []

            print(f"\n{Fore.CYAN}文件列表:{Style.RESET_ALL}")
            print(f"{'类型':<6} {'名称':<30} {'大小':<12} {'修改时间':<20} {'ID'}")
            print("-" * 80)

            for file_info in files:
                if isinstance(file_info, str):
                    continue  # 跳过字符串类型的项目

                file_type = "文件夹" if file_info.get('type') == 1 else "文件"
                name = str(file_info.get('filename', 'Unknown'))[:28]
                size = self.format_size(file_info.get('size', 0)) if file_info.get('type') != 1 else '-'
                modified = str(file_info.get('updateAt', 'Unknown'))[:19]
                file_id = str(file_info.get('fileId', 'Unknown'))

                color = Fore.BLUE if file_type == "文件夹" else Fore.WHITE
                print(f"{color}{file_type:<6} {name:<30} {size:<12} {modified:<20} {file_id}{Style.RESET_ALL}")

            return files

        except Exception as e:
            print(f"{Fore.RED}获取文件列表失败: {e}{Style.RESET_ALL}")
            return []

    def create_folder_api(self, name: str, parent_id: int = 0) -> Optional[Dict]:
        """使用官方API创建文件夹"""
        try:
            # 尝试使用文件夹创建API
            conn = http.client.HTTPSConnection("open-api.123pan.com")

            # 方法1: 尝试使用mkdir API
            payload = json.dumps({
                "parentFileID": parent_id,
                "filename": name
            })

            headers = {
                'Content-Type': 'application/json',
                'Platform': 'open_platform',
                'Authorization': f'Bearer {self.config["auth"]["access_token"]}'
            }

            # 尝试不同的API端点
            endpoints = [
                "/upload/v2/file/mkdir",  # 可能的文件夹创建端点
                "/api/v1/file/mkdir",     # 另一个可能的端点
                "/file/mkdir"             # 简化端点
            ]

            for endpoint in endpoints:
                try:
                    conn.request("POST", endpoint, payload, headers)
                    res = conn.getresponse()
                    data = res.read()
                    result = json.loads(data.decode("utf-8"))

                    if result.get('code') == 0:
                        print(f"{Fore.GREEN}文件夹创建成功: {name} (使用端点: {endpoint}){Style.RESET_ALL}")
                        return result.get('data')
                    else:
                        print(f"{Fore.YELLOW}端点 {endpoint} 失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")

                except Exception as e:
                    print(f"{Fore.YELLOW}端点 {endpoint} 异常: {e}{Style.RESET_ALL}")
                    continue

            # 如果所有端点都失败，回退到原来的方法
            print(f"{Fore.YELLOW}尝试使用文件创建API创建文件夹...{Style.RESET_ALL}")
            payload = json.dumps({
                "parentFileID": parent_id,
                "filename": name,
                "etag": "",
                "size": 0
            })

            conn.request("POST", "/upload/v2/file/create", payload, headers)
            res = conn.getresponse()
            data = res.read()
            result = json.loads(data.decode("utf-8"))

            if result.get('code') == 0:
                print(f"{Fore.GREEN}文件夹创建成功: {name}{Style.RESET_ALL}")
                return result.get('data')
            else:
                print(f"{Fore.RED}文件夹创建失败: {result.get('message', 'Unknown error')}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}API调用失败: {e}{Style.RESET_ALL}")
            return None

    def find_or_create_folder(self, name: str, parent_id: int = 0) -> Optional[Dict]:
        """查找或创建文件夹"""
        try:
            # 首先检查文件夹是否已存在
            result = self.pan.file.list(parent_id, limit=100)
            if isinstance(result, dict) and 'fileList' in result:
                files = result['fileList']
                for file_info in files:
                    if (file_info.get('filename') == name and
                        file_info.get('type') == 1):  # type=1表示文件夹
                        print(f"{Fore.GREEN}找到已存在文件夹: {name} (ID: {file_info.get('fileId')}){Style.RESET_ALL}")
                        return file_info

            # 如果文件夹不存在，尝试创建
            print(f"{Fore.CYAN}正在尝试创建文件夹: {name}{Style.RESET_ALL}")
            folder_result = self.create_folder_api(name, parent_id)

            if folder_result:
                return folder_result
            else:
                # 如果API创建失败，提供用户选择
                print(f"{Fore.YELLOW}无法自动创建文件夹 '{name}'{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}建议：请在123网盘网页版手动创建文件夹，然后重新运行程序{Style.RESET_ALL}")

                # 显示当前目录下的文件夹供用户选择
                print(f"\n{Fore.CYAN}当前目录下的现有文件夹:{Style.RESET_ALL}")
                folder_count = 0
                for file_info in files:
                    if file_info.get('type') == 1:  # 文件夹
                        folder_count += 1
                        print(f"  {folder_count}. {file_info.get('filename')} (ID: {file_info.get('fileId')})")

                if folder_count == 0:
                    print(f"  {Fore.YELLOW}当前目录下没有文件夹{Style.RESET_ALL}")

                return None

        except Exception as e:
            print(f"{Fore.RED}查找或创建文件夹失败: {e}{Style.RESET_ALL}")
            return None

    def create_folder(self, name: str, parent_id: int = 0) -> Optional[Dict]:
        """创建文件夹（兼容性方法）"""
        return self.find_or_create_folder(name, parent_id)

    def delete_file(self, file_id: str) -> bool:
        """删除文件或文件夹"""
        try:
            # 获取文件信息用于确认
            file_info = self.get_file_info(file_id)
            if not file_info:
                return False

            filename = file_info.get('name', 'Unknown')
            file_type = "文件夹" if file_info.get('type') == 'folder' else "文件"

            # 确认删除
            confirm = input(f"{Fore.YELLOW}确认删除{file_type}: {filename}? (y/N): {Style.RESET_ALL}")
            if confirm.lower() != 'y':
                print(f"{Fore.YELLOW}取消删除{Style.RESET_ALL}")
                return False

            self.pan.file.delete(file_id)
            print(f"{Fore.GREEN}{file_type}删除成功: {filename}{Style.RESET_ALL}")
            return True

        except Exception as e:
            print(f"{Fore.RED}删除失败: {e}{Style.RESET_ALL}")
            return False

    def get_file_info(self, file_id: str) -> Optional[Dict]:
        """获取文件信息"""
        try:
            return self.pan.file.info(file_id)
        except Exception as e:
            print(f"{Fore.RED}获取文件信息失败: {e}{Style.RESET_ALL}")
            return None

    def search_files(self, keyword: str) -> List[Dict]:
        """搜索文件"""
        try:
            results = self.pan.file.search(keyword)

            if not results:
                print(f"{Fore.YELLOW}未找到匹配的文件{Style.RESET_ALL}")
                return []

            print(f"\n{Fore.CYAN}搜索结果 (关键词: {keyword}):{Style.RESET_ALL}")
            print(f"{'类型':<6} {'名称':<30} {'大小':<12} {'路径':<20} {'ID'}")
            print("-" * 80)

            for file_info in results:
                file_type = "文件夹" if file_info.get('type') == 'folder' else "文件"
                name = file_info.get('name', 'Unknown')[:28]
                size = self.format_size(file_info.get('size', 0)) if file_info.get('type') != 'folder' else '-'
                path = file_info.get('path', 'Unknown')[:18]
                file_id = file_info.get('id', 'Unknown')

                color = Fore.BLUE if file_type == "文件夹" else Fore.WHITE
                print(f"{color}{file_type:<6} {name:<30} {size:<12} {path:<20} {file_id}{Style.RESET_ALL}")

            return results

        except Exception as e:
            print(f"{Fore.RED}搜索失败: {e}{Style.RESET_ALL}")
            return []


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="123网盘管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s auth                          # 重新认证
  %(prog)s upload file.txt               # 上传文件到根目录
  %(prog)s upload folder/                # 上传文件夹到根目录
  %(prog)s download 123456               # 下载文件ID为123456的文件
  %(prog)s download 123456 ./downloads/  # 下载到指定目录
  %(prog)s list                          # 列出根目录文件
  %(prog)s list 123456                   # 列出指定文件夹内容
  %(prog)s mkdir "新文件夹"               # 在根目录创建文件夹
  %(prog)s rm 123456                     # 删除文件或文件夹
  %(prog)s info 123456                   # 查看文件信息
  %(prog)s search "关键词"               # 搜索文件
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 认证命令
    subparsers.add_parser('auth', help='重新认证')

    # 上传命令
    upload_parser = subparsers.add_parser('upload', help='上传文件或文件夹')
    upload_parser.add_argument('path', help='要上传的文件或文件夹路径')
    upload_parser.add_argument('--parent-id', type=int, default=0, help='上传到的父文件夹ID (默认: 0-根目录)')
    upload_parser.add_argument('--flat', action='store_true', help='平铺上传，不保持目录结构')

    # 下载命令
    download_parser = subparsers.add_parser('download', help='下载文件')
    download_parser.add_argument('file_id', help='要下载的文件ID')
    download_parser.add_argument('save_path', nargs='?', help='保存路径 (可选)')

    # 列表命令
    list_parser = subparsers.add_parser('list', help='列出文件和文件夹')
    list_parser.add_argument('parent_id', nargs='?', type=int, default=0, help='文件夹ID (默认: 0-根目录)')

    # 创建文件夹命令
    mkdir_parser = subparsers.add_parser('mkdir', help='创建文件夹')
    mkdir_parser.add_argument('name', help='文件夹名称')
    mkdir_parser.add_argument('--parent-id', type=int, default=0, help='父文件夹ID (默认: 0-根目录)')

    # 删除命令
    rm_parser = subparsers.add_parser('rm', help='删除文件或文件夹')
    rm_parser.add_argument('file_id', help='要删除的文件或文件夹ID')

    # 文件信息命令
    info_parser = subparsers.add_parser('info', help='查看文件信息')
    info_parser.add_argument('file_id', help='文件ID')

    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索文件')
    search_parser.add_argument('keyword', help='搜索关键词')

    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        # 初始化管理器
        manager = Pan123Manager()

        # 执行相应命令
        if args.command == 'auth':
            print(f"{Fore.GREEN}重新认证完成{Style.RESET_ALL}")

        elif args.command == 'upload':
            path = Path(args.path)
            print(f"{Fore.CYAN}上传目标目录ID: {args.parent_id}{Style.RESET_ALL}")
            if hasattr(args, 'flat') and args.flat:
                print(f"{Fore.YELLOW}使用平铺模式上传（不保持目录结构）{Style.RESET_ALL}")

            if path.is_file():
                manager.upload_file(args.path, args.parent_id)
            elif path.is_dir():
                create_structure = not (hasattr(args, 'flat') and args.flat)
                manager.upload_folder(args.path, args.parent_id, create_structure)
            else:
                print(f"{Fore.RED}路径不存在: {args.path}{Style.RESET_ALL}")

        elif args.command == 'download':
            manager.download_file(args.file_id, args.save_path)

        elif args.command == 'list':
            manager.list_files(args.parent_id)

        elif args.command == 'mkdir':
            manager.create_folder(args.name, args.parent_id)

        elif args.command == 'rm':
            manager.delete_file(args.file_id)

        elif args.command == 'info':
            file_info = manager.get_file_info(args.file_id)
            if file_info:
                print(f"\n{Fore.CYAN}文件信息:{Style.RESET_ALL}")
                for key, value in file_info.items():
                    print(f"{key}: {value}")

        elif args.command == 'search':
            manager.search_files(args.keyword)

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}操作已取消{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}程序执行出错: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
