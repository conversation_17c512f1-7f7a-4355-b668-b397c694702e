#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
123网盘管理工具测试脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from pan123 import Pan123
    from pan123.auth import get_access_token
    import yaml
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

def test_basic_functionality():
    """测试基本功能"""
    print("=== 123网盘管理工具测试 ===\n")
    
    # 加载配置
    try:
        with open("123_config.yml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 配置文件加载成功")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return
    
    # 初始化客户端
    try:
        access_token = config['auth']['access_token']
        pan = Pan123(access_token)
        print("✅ 123网盘客户端初始化成功")
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
        return
    
    # 测试文件列表
    try:
        result = pan.file.list(0, limit=10)
        print(f"✅ 文件列表获取成功，类型: {type(result)}")
        if isinstance(result, dict):
            print(f"   结果键: {list(result.keys())}")
        elif isinstance(result, list):
            print(f"   列表长度: {len(result)}")
    except Exception as e:
        print(f"❌ 文件列表获取失败: {e}")
    
    # 测试用户信息
    try:
        user_info = pan.user.info()
        print(f"✅ 用户信息获取成功: {user_info.get('nickname', 'Unknown')}")
    except Exception as e:
        print(f"❌ 用户信息获取失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_basic_functionality()
