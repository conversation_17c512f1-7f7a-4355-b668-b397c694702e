# 123云盘上传工具配置文件 (基于 pan123 库)
# 参考文档: https://sodacodesave.github.io/Pan123-Docs/site/

# 认证配置
auth:
  # 123云盘开放平台应用信息（必填）
  # 获取方式：访问 https://open.123pan.com/ 创建应用
  client_id: "51be3a9ec02047de9f0f6f1c74615fa8" # 应用ID
  client_secret: "830ec51950634807a54642d213a5c014" # 应用密钥
  redirect_uri: "http://localhost" # 回调地址

  # 访问令牌（认证成功后自动保存）
  access_token: "" # 访问令牌
  refresh_token: "" # 刷新令牌

# 上传配置
upload:
  default_parent_id: 0 # 默认上传目录ID，0表示根目录
  chunk_size: 8388608 # 分块上传大小（8MB = 8*1024*1024字节）
  max_retries: 3 # 最大重试次数
  overwrite: false # 是否覆盖同名文件

# OSS图床配置
oss:
  enabled: false # 是否启用OSS图床功能
  allowed_extensions: # 支持的图片格式
    - ".jpg"
    - ".png"
    - ".jpeg"
    - ".gif"
    - ".bmp"
    - ".webp"
# 使用说明:
# 1. 安装依赖: pip install pan123 pyyaml
# 2. 确保已填写正确的 client_id 和 client_secret
# 3. 运行程序进行首次认证: python 123.py user-info
# 4. 开始使用各种功能:
#    - 上传文件: python 123.py upload file.txt
#    - 上传文件夹: python 123.py upload-folder /path/to/folder
#    - 上传图片: python 123.py upload-image image.jpg
#    - 查看文件: python 123.py list
