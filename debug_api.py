#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试123网盘API数据结构
"""

import yaml
import json
from pan123 import Pan123

# 加载配置
with open("123_config.yml", 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 初始化客户端
access_token = config['auth']['access_token']
pan = Pan123(access_token)

# 获取文件列表
result = pan.file.list(0, limit=5)
print("API返回结果:")
print(json.dumps(result, indent=2, ensure_ascii=False))

if isinstance(result, dict) and 'fileList' in result:
    files = result['fileList']
    if files:
        print("\n第一个文件的字段:")
        first_file = files[0]
        for key, value in first_file.items():
            print(f"  {key}: {value}")

# 测试创建文件夹
print("\n=== 测试创建文件夹 ===")
try:
    # 尝试不同的方法
    print("可用的文件操作方法:")
    file_methods = [method for method in dir(pan.file) if not method.startswith('_')]
    for method in file_methods:
        print(f"  - {method}")

    # 尝试创建文件夹 - 使用create方法创建文件夹
    if hasattr(pan.file, 'create'):
        print("\n尝试使用create方法创建文件夹...")
        try:
            # 对于文件夹：parent_id, name, etag="", size=0
            # 根据API文档，文件夹的etag可以为空字符串，size为0
            create_result = pan.file.create(0, "测试文件夹API4", "", 0)
            print(f"create文件夹结果: {create_result}")
        except Exception as e:
            print(f"create文件夹方法失败: {e}")

    # 检查是否有其他创建文件夹的方法
    print("\n查找所有可能的文件夹创建方法:")
    all_methods = dir(pan.file)
    folder_methods = [m for m in all_methods if 'fold' in m.lower() or 'dir' in m.lower() or 'mkdir' in m.lower()]
    print(f"可能的文件夹相关方法: {folder_methods}")

    # 检查create方法的签名
    import inspect
    if hasattr(pan.file, 'create'):
        print(f"\ncreate方法签名: {inspect.signature(pan.file.create)}")
    if hasattr(pan.file, 'mkdir'):
        print(f"mkdir方法签名: {inspect.signature(pan.file.mkdir)}")

except Exception as e:
    print(f"创建文件夹测试失败: {e}")
