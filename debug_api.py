#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试123网盘API数据结构
"""

import yaml
import json
from pan123 import Pan123

# 加载配置
with open("123_config.yml", 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 初始化客户端
access_token = config['auth']['access_token']
pan = Pan123(access_token)

# 获取文件列表
result = pan.file.list(0, limit=5)
print("API返回结果:")
print(json.dumps(result, indent=2, ensure_ascii=False))

if isinstance(result, dict) and 'fileList' in result:
    files = result['fileList']
    if files:
        print("\n第一个文件的字段:")
        first_file = files[0]
        for key, value in first_file.items():
            print(f"  {key}: {value}")

# 测试创建文件夹
print("\n=== 测试创建文件夹 ===")
try:
    # 尝试不同的方法
    print("可用的文件操作方法:")
    file_methods = [method for method in dir(pan.file) if not method.startswith('_')]
    for method in file_methods:
        print(f"  - {method}")

    # 尝试创建文件夹
    if hasattr(pan.file, 'mkdir'):
        print("\n尝试使用mkdir方法...")
        mkdir_result = pan.file.mkdir(0, "测试文件夹API")
        print(f"mkdir结果: {mkdir_result}")

    if hasattr(pan.file, 'create_folder'):
        print("\n尝试使用create_folder方法...")
        create_result = pan.file.create_folder(0, "测试文件夹API2")
        print(f"create_folder结果: {create_result}")

except Exception as e:
    print(f"创建文件夹测试失败: {e}")
