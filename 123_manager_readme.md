# 123网盘管理工具

基于123网盘官方API开发的命令行管理工具，支持文件上传、下载、管理等功能。

## 功能特性

- ✅ 文件上传（单文件和文件夹批量上传）
- ✅ 文件下载（支持进度显示）
- ✅ 文件列表查看
- ✅ 文件夹创建
- ✅ 文件删除（带确认）
- ✅ 文件搜索
- ✅ 文件信息查看
- ✅ 彩色输出和友好的用户界面
- ✅ 完整的错误处理

## 安装依赖

```bash
pip install pan123 tqdm colorama pyyaml requests
```

## 配置文件

程序使用 `123_config.yml` 配置文件，请确保已正确配置：

```yaml
auth:
  client_id: "你的应用ID"
  client_secret: "你的应用密钥"
  access_token: ""  # 首次运行后自动获取并保存
  
upload:
  default_parent_id: 0  # 默认上传到根目录
  chunk_size: 8388608   # 8MB分块大小
  max_retries: 3        # 最大重试次数
  overwrite: false      # 是否覆盖同名文件
```

## 使用方法

### 基本命令

```bash
# 查看帮助
python 123_manager.py -h

# 重新认证（首次使用或token过期时）
python 123_manager.py auth

# 上传文件到根目录
python 123_manager.py upload file.txt

# 上传文件夹到根目录
python 123_manager.py upload folder/

# 上传到指定文件夹（需要文件夹ID）
python 123_manager.py upload file.txt --parent-id 123456

# 下载文件（需要文件ID）
python 123_manager.py download 123456

# 下载到指定目录
python 123_manager.py download 123456 ./downloads/

# 列出根目录文件
python 123_manager.py list

# 列出指定文件夹内容
python 123_manager.py list 123456

# 创建文件夹
python 123_manager.py mkdir "新文件夹"

# 在指定位置创建文件夹
python 123_manager.py mkdir "新文件夹" --parent-id 123456

# 删除文件或文件夹（会要求确认）
python 123_manager.py rm 123456

# 查看文件详细信息
python 123_manager.py info 123456

# 搜索文件
python 123_manager.py search "关键词"
```

### 获取123网盘开发者权限

1. 访问 [123网盘开放平台](https://open.123pan.com/)
2. 注册并创建应用
3. 获取 `client_id` 和 `client_secret`
4. 将这些信息填入 `123_config.yml` 配置文件

## 注意事项

1. **首次使用**：运行任何命令前，程序会自动进行认证并保存access_token
2. **文件ID获取**：通过 `list` 命令可以查看文件和文件夹的ID
3. **大文件上传**：程序支持大文件分块上传，会自动处理
4. **错误处理**：程序包含完整的错误处理，会显示详细的错误信息
5. **进度显示**：上传和下载操作会显示进度条
6. **安全确认**：删除操作需要用户确认

## 常见问题

### Q: 认证失败怎么办？
A: 检查配置文件中的client_id和client_secret是否正确，或运行 `python 123_manager.py auth` 重新认证。

### Q: 如何获取文件ID？
A: 使用 `python 123_manager.py list` 命令查看文件列表，最后一列显示的就是文件ID。

### Q: 上传大文件很慢？
A: 这是正常现象，程序会自动进行分块上传，请耐心等待。

### Q: 下载的文件保存在哪里？
A: 默认保存在当前目录，也可以通过参数指定保存路径。

## 技术实现

- 基于 `pan123` 官方Python SDK
- 使用 `tqdm` 提供进度条显示
- 使用 `colorama` 提供彩色输出
- 使用 `pyyaml` 处理配置文件
- 完整的命令行参数解析
- 异常处理和用户友好的错误提示

## 开发者信息

本工具基于123网盘官方API开发，遵循官方开发文档规范。
