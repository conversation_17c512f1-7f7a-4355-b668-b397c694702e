#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
hanime1.me 视频抓取和刮削整合脚本
先执行 craw.py 的爬虫功能，然后执行 hanimecraw.py 的刮削功能
"""

import os
import sys
import yaml
from pathlib import Path
import time
from datetime import datetime

# 导入爬虫模块
import craw
# 导入刮削模块
from hanimecraw import HanimeScraper

def load_config(config_path="config.yml"):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"配置文件 {config_path} 不存在，使用默认配置")
        return None
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None

def run_hanime_scraper(download_dir):
    """运行 hanime 刮削器，对下载的视频文件进行刮削"""
    print("\n" + "="*50)
    print("开始执行 hanime1.me 视频刮削...")
    print("="*50)
    
    # 创建刮削器实例
    scraper = HanimeScraper()
    
    # 查找所有下载的视频文件
    video_files = []
    if os.path.exists(download_dir):
        for root, dirs, files in os.walk(download_dir):
            for file in files:
                if file.endswith('.mp4'):
                    video_path = os.path.join(root, file)
                    video_files.append(video_path)
    
    if not video_files:
        print("没有找到需要刮削的视频文件")
        return
    
    print(f"找到 {len(video_files)} 个视频文件，开始刮削...")
    
    # 对每个视频文件进行刮削
    for idx, video_path in enumerate(video_files, 1):
        print(f"\n[{idx}/{len(video_files)}] 刮削文件: {os.path.basename(video_path)}")
        
        try:
            # 从文件名提取标题
            filename = os.path.splitext(os.path.basename(video_path))[0]
            # 移除 -poster 等后缀
            title = filename.replace('-poster', '')
            
            # 调用刮削功能
            info = scraper.scrape_hanime1_by_title(title)
            
            if info:
                print(f"   ✓ 获取到信息: {info.get('title', '未知标题')}")
                
                # 生成 NFO 文件
                nfo_content = scraper.generate_nfo(info)
                if nfo_content:
                    nfo_path = os.path.splitext(video_path)[0] + '.nfo'
                    with open(nfo_path, 'w', encoding='utf-8') as f:
                        f.write(nfo_content)
                    print(f"   ✓ NFO 文件已生成: {nfo_path}")
                
                # 下载图片
                image_url = info.get('image_url')
                if image_url:
                    # 获取视频文件所在目录
                    video_dir = os.path.dirname(video_path)
                    image_filename = scraper.download_image_to_dir(image_url, title, video_dir)
                    if image_filename:
                        print(f"   ✓ 图片下载完成: {image_filename}")
                    else:
                        print(f"   ✗ 图片下载失败")
                
            else:
                print(f"   ✗ 未找到相关信息")
                
        except Exception as e:
            print(f"   ✗ 刮削失败: {e}")
        
        # 添加延迟避免请求过于频繁
        time.sleep(1)
    
    print("\nhanime1.me 视频刮削完成！")

def main():
    """主函数：整合执行爬虫和刮削功能"""
    print("hanime1.me 视频抓取和刮削整合脚本")
    print("="*50)
    
    # 加载配置
    config = load_config()
    
    # 获取下载目录
    if config:
        download_dir = config.get('download', {}).get('download_dir', 'downloads')
        organize_by_date = config.get('download', {}).get('organize_by_date', True)
        target_year = config.get('crawl', {}).get('date_filter', {}).get('year', 2025)
        target_month = config.get('crawl', {}).get('date_filter', {}).get('month', 1)
        
        # 确保年月不为 None，使用上一个月作为默认值
        if target_year is None or target_month is None:
            today = datetime.now()
            if today.month == 1:
                default_year, default_month = today.year - 1, 12
            else:
                default_year, default_month = today.year, today.month - 1
            
            if target_year is None:
                target_year = default_year
            if target_month is None:
                target_month = default_month
            
        if organize_by_date:
            download_dir = os.path.join(download_dir, str(target_year), f"{target_month:02d}")
    else:
        download_dir = 'downloads'
    
    try:
        # 第一步：运行爬虫
        print("第一步：执行视频爬取...")
        success = craw.run_crawler()
        
        if success:
            print("\n视频爬取完成，等待3秒后开始刮削...")
            time.sleep(3)
            
            # 第二步：运行刮削器
            run_hanime_scraper(download_dir)
            
            print("\n" + "="*50)
            print("所有任务完成！")
            print("="*50)
        else:
            print("爬虫执行失败，跳过刮削步骤")
            
    except KeyboardInterrupt:
        print("\n\n用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
